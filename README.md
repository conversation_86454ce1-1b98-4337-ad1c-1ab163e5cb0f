# نقرة هوست - Nakra Host

موقع احترافي لشركة نقرة هوست المتخصصة في خدمات الاستضافة والتسويق الإلكتروني.

## 🚀 رفع الموقع على الاستضافة

### متطلبات الاستضافة
- **PHP 7.4** أو أحدث
- **Apache** مع mod_rewrite مفعل
- **دعم البريد الإلكتروني** لنموذج الاتصال
- **SSL Certificate** (مستحسن)

### خطوات الرفع
1. ارفع جميع الملفات إلى مجلد public_html
2. تأكد من أن ملف .htaccess تم رفعه
3. اضبط صلاحيات المجلدات إلى 755
4. اضبط صلاحيات الملفات إلى 644
5. تأكد من عمل البريد الإلكتروني في contact.php

## 🌟 المميزات

### خدمات الاستضافة
- **الاستضافة المشتركة**: خطط متنوعة تناسب جميع الاحتياجات
- **استضافة الريسيلر**: حلول مربحة لبدء عملك في مجال الاستضافة
- **استضافة ووردبريس**: محسنة خصيصاً لمواقع ووردبريس

### خدمات التسويق الإلكتروني
- **إعلانات جوجل**: حملات احترافية لزيادة المبيعات
- **إعلانات فيسبوك وإنستجرام**: استراتيجيات متقدمة للتواصل الاجتماعي
- **إعلانات تيك توك وسناب شات**: الوصول للجيل الجديد من العملاء

### خدمات إضافية
- **تصميم وتطوير المواقع**: مواقع احترافية متجاوبة
- **إدارة السيرفرات**: مراقبة وصيانة على مدار الساعة
- **الأمان والحماية**: حلول أمنية شاملة

## 🎨 التقنيات المستخدمة

- **PHP**: للصفحات الديناميكية ومعالجة النماذج
- **HTML5**: هيكل حديث ومتوافق مع المعايير
- **CSS3**: تصميم متجاوب مع انيميشن احترافي
- **JavaScript**: تفاعلات متقدمة وأداء محسن
- **Font Awesome**: أيقونات احترافية
- **Google Fonts**: خطوط عربية وإنجليزية عالية الجودة

## ✅ المميزات التقنية
- تصميم متجاوب (Responsive Design)
- دعم اللغتين العربية والإنجليزية
- انيميشن متقدم وتفاعلي
- تحسين محركات البحث (SEO)
- أداء سريع ومحسن
- متوافق مع جميع المتصفحات
- نموذج اتصال يعمل بـ PHP

## 📁 هيكل المشروع

```
nakra-host/
├── index.php             # الصفحة الرئيسية
├── contact.php           # معالج نموذج الاتصال
├── .htaccess            # إعدادات Apache
├── manifest.json        # ملف PWA
├── sw.js               # Service Worker
├── css/
│   ├── style.css       # الأنماط الرئيسية
│   ├── animations.css  # انيميشن وتأثيرات
│   └── responsive.css  # التصميم المتجاوب
├── js/
│   ├── main.js         # الوظائف الرئيسية
│   ├── animations.js   # تحكم في الانيميشن
│   └── language.js     # نظام اللغات
├── images/             # الصور والرسوم
└── README.md          # هذا الملف
```

## ⚙️ الإعدادات المطلوبة

### تعديل معلومات الاتصال
في ملف `contact.php`:
```php
$to_email = '<EMAIL>';        // بريدك الإلكتروني
$from_email = '<EMAIL>';   // بريد الإرسال
```

### تعديل الروابط
في ملف `index.php`:
```php
$main_site = "https://hostmeed.cloud";           // الموقع الرئيسي
$marketing_site = "https://nakraformarketing.com"; // موقع التسويق
```

## 🎯 الصفحات والوظائف

### الصفحة الرئيسية (index.html)
- **القسم الرئيسي**: عرض تقديمي جذاب مع انيميشن
- **المميزات**: عرض نقاط القوة الرئيسية
- **الخدمات**: تفصيل شامل لجميع الخدمات
- **الأسعار**: خطط واضحة ومقارنة
- **من نحن**: معلومات عن الشركة وإحصائيات
- **تواصل معنا**: نموذج اتصال تفاعلي

### صفحة تسجيل الدخول (login.html)
- نموذج تسجيل دخول آمن
- خيارات تسجيل الدخول الاجتماعي
- ربط مع الموقع الرئيسي hostmeed.cloud

### صفحة إنشاء الحساب (register.html)
- نموذج تسجيل شامل مع التحقق
- مؤشر قوة كلمة المرور
- ربط مع الموقع الرئيسي hostmeed.cloud

## 🌐 نظام اللغات

الموقع يدعم اللغتين العربية والإنجليزية مع:
- تبديل سلس بين اللغات
- حفظ تفضيل اللغة
- تغيير اتجاه النص (RTL/LTR)
- ترجمة شاملة لجميع العناصر

## 🎨 نظام الألوان

```css
/* الألوان الأساسية */
--primary-color: #2563eb;    /* أزرق أساسي */
--secondary-color: #10b981;  /* أخضر ثانوي */
--accent-color: #f59e0b;     /* برتقالي مميز */
--dark-color: #1f2937;       /* رمادي داكن */
--light-color: #f8fafc;      /* رمادي فاتح */
```

## 📱 التوافق والاستجابة

الموقع متوافق مع:
- **أجهزة سطح المكتب**: 1200px وأكثر
- **الأجهزة اللوحية**: 768px - 1199px
- **الهواتف الذكية**: أقل من 768px

## 🔧 التخصيص والتطوير

### إضافة خدمة جديدة
1. أضف المحتوى في القسم المناسب في `index.html`
2. أضف الأنماط في `css/style.css`
3. أضف الترجمة في `js/language.js`

### تخصيص الألوان
عدّل المتغيرات في بداية ملف `css/style.css`:
```css
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
    /* ... */
}
```

### إضافة انيميشن جديد
1. أضف keyframes في `css/animations.css`
2. أضف الكلاس المناسب
3. استخدم `AnimationController` في `js/animations.js`

## 🔗 الروابط المهمة

- **الموقع الرئيسي**: [hostmeed.cloud](https://hostmeed.cloud)
- **شركة التسويق**: [nakraformarketing.com](https://nakraformarketing.com)

## 📞 معلومات الاتصال

- **الهاتف**: +201028351237
- **البريد الإلكتروني**: <EMAIL>
- **العنوان**: مصر - نبروة سيتي، قرية نشا، شارع السوق

## 👥 فريق التطوير

تم إنشاء هذا الموقع بواسطة فريق **نقرة للتسويق الإلكتروني**.

## 📄 الترخيص

جميع الحقوق محفوظة © 2024 نقرة هوست

---

**ملاحظة**: هذا الموقع هو واجهة تسويقية تتصل بالموقع الرئيسي hostmeed.cloud لإتمام عمليات التسجيل والشراء.
