<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - نقرة هوست</title>
    <meta name="description" content="إنشاء حساب جديد في نقرة هوست للحصول على أفضل خدمات الاستضافة والتسويق الإلكتروني">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body class="auth-page">
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button id="langToggle" class="lang-btn">
            <i class="fas fa-globe"></i>
            <span class="lang-text">English</span>
        </button>
    </div>

    <!-- Auth Container -->
    <div class="auth-container">
        <div class="auth-background">
            <div class="auth-particles"></div>
        </div>
        
        <div class="auth-content">
            <!-- Logo Section -->
            <div class="auth-logo">
                <img src="images/logo.png" alt="نقرة هوست" class="logo">
                <h1 class="brand-name" data-ar="نقرة هوست" data-en="Nakra Host">نقرة هوست</h1>
                <p class="brand-tagline" data-ar="حلولك الرقمية المتكاملة" data-en="Your Complete Digital Solutions">حلولك الرقمية المتكاملة</p>
            </div>

            <!-- Register Form -->
            <div class="auth-form-container animate-fade-up">
                <div class="auth-form-header">
                    <h2 data-ar="إنشاء حساب جديد" data-en="Create New Account">إنشاء حساب جديد</h2>
                    <p data-ar="انضم إلينا واحصل على أفضل خدمات الاستضافة والتسويق الإلكتروني" 
                       data-en="Join us and get the best hosting and digital marketing services">
                        انضم إلينا واحصل على أفضل خدمات الاستضافة والتسويق الإلكتروني
                    </p>
                </div>

                <form class="auth-form" id="registerForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName" data-ar="الاسم الأول" data-en="First Name">الاسم الأول</label>
                            <div class="input-group">
                                <i class="fas fa-user"></i>
                                <input type="text" id="firstName" name="firstName" required 
                                       placeholder="أحمد">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="lastName" data-ar="الاسم الأخير" data-en="Last Name">الاسم الأخير</label>
                            <div class="input-group">
                                <i class="fas fa-user"></i>
                                <input type="text" id="lastName" name="lastName" required 
                                       placeholder="محمد">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email" data-ar="البريد الإلكتروني" data-en="Email Address">البريد الإلكتروني</label>
                        <div class="input-group">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="email" name="email" required 
                                   placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="phone" data-ar="رقم الهاتف" data-en="Phone Number">رقم الهاتف</label>
                        <div class="input-group">
                            <i class="fas fa-phone"></i>
                            <input type="tel" id="phone" name="phone" required 
                                   placeholder="+966 50 123 4567">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" data-ar="كلمة المرور" data-en="Password">كلمة المرور</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" required 
                                   placeholder="••••••••">
                            <button type="button" class="password-toggle" id="passwordToggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="passwordStrength">
                            <div class="strength-bar">
                                <div class="strength-fill"></div>
                            </div>
                            <span class="strength-text" data-ar="قوة كلمة المرور" data-en="Password Strength">قوة كلمة المرور</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword" data-ar="تأكيد كلمة المرور" data-en="Confirm Password">تأكيد كلمة المرور</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="confirmPassword" name="confirmPassword" required 
                                   placeholder="••••••••">
                            <button type="button" class="password-toggle" id="confirmPasswordToggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="company" data-ar="اسم الشركة (اختياري)" data-en="Company Name (Optional)">اسم الشركة (اختياري)</label>
                        <div class="input-group">
                            <i class="fas fa-building"></i>
                            <input type="text" id="company" name="company" 
                                   placeholder="شركة التقنية المتقدمة">
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="terms" name="terms" required>
                            <span class="checkmark"></span>
                            <span>
                                <span data-ar="أوافق على" data-en="I agree to">أوافق على</span>
                                <a href="terms.html" target="_blank" data-ar="الشروط والأحكام" data-en="Terms & Conditions">الشروط والأحكام</a>
                                <span data-ar="و" data-en="and">و</span>
                                <a href="privacy.html" target="_blank" data-ar="سياسة الخصوصية" data-en="Privacy Policy">سياسة الخصوصية</a>
                            </span>
                        </label>

                        <label class="checkbox-container">
                            <input type="checkbox" id="newsletter" name="newsletter">
                            <span class="checkmark"></span>
                            <span data-ar="أريد تلقي النشرة الإخبارية والعروض الخاصة" 
                                  data-en="I want to receive newsletter and special offers">
                                أريد تلقي النشرة الإخبارية والعروض الخاصة
                            </span>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full">
                        <span data-ar="إنشاء الحساب" data-en="Create Account">إنشاء الحساب</span>
                        <i class="fas fa-arrow-left"></i>
                    </button>

                    <div class="divider">
                        <span data-ar="أو" data-en="or">أو</span>
                    </div>

                    <div class="social-login">
                        <button type="button" class="btn btn-social btn-google">
                            <i class="fab fa-google"></i>
                            <span data-ar="التسجيل بجوجل" data-en="Sign up with Google">التسجيل بجوجل</span>
                        </button>
                        <button type="button" class="btn btn-social btn-facebook">
                            <i class="fab fa-facebook-f"></i>
                            <span data-ar="التسجيل بفيسبوك" data-en="Sign up with Facebook">التسجيل بفيسبوك</span>
                        </button>
                    </div>

                    <div class="auth-footer">
                        <p>
                            <span data-ar="لديك حساب بالفعل؟" data-en="Already have an account?">لديك حساب بالفعل؟</span>
                            <a href="login.html" data-ar="تسجيل الدخول" data-en="Login">تسجيل الدخول</a>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Redirect to Main Site -->
            <div class="redirect-notice animate-fade-up" data-delay="200">
                <div class="notice-content">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <h4 data-ar="التسجيل في الموقع الرئيسي" data-en="Register on Main Site">التسجيل في الموقع الرئيسي</h4>
                        <p data-ar="سيتم توجيهك إلى موقعنا الرئيسي لإكمال التسجيل" 
                           data-en="You will be redirected to our main site to complete registration">
                            سيتم توجيهك إلى موقعنا الرئيسي لإكمال التسجيل
                        </p>
                        <a href="https://hostmeed.cloud/register.php" class="btn btn-outline btn-sm" target="_blank">
                            <span data-ar="التسجيل في hostmeed.cloud" data-en="Register on hostmeed.cloud">التسجيل في hostmeed.cloud</span>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Background Elements -->
        <div class="auth-decorations">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>
    </div>

    <!-- Footer Credit -->
    <footer class="auth-credit">
        <p>
            <span data-ar="تم إنشاء الموقع بواسطة فريق" data-en="Website created by">تم إنشاء الموقع بواسطة فريق</span>
            <a href="https://nakraformarketing.com" target="_blank">نقرة للتسويق الإلكتروني</a>
        </p>
    </footer>

    <!-- Scripts -->
    <script src="js/language.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize register form
        document.addEventListener('DOMContentLoaded', function() {
            initializeRegisterForm();
            initializePasswordToggles();
            initializePasswordStrength();
            initializeSocialLogin();
        });

        function initializeRegisterForm() {
            const registerForm = document.getElementById('registerForm');
            
            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                
                // Validate form
                if (validateRegisterForm(data)) {
                    // Show loading state
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span data-ar="جاري إنشاء الحساب..." data-en="Creating account...">جاري إنشاء الحساب...</span>';
                    submitBtn.disabled = true;
                    
                    // Simulate registration process
                    setTimeout(() => {
                        // In a real application, you would send this data to your server
                        console.log('Registration attempt:', data);
                        
                        // Redirect to hostmeed.cloud
                        window.open('https://hostmeed.cloud/register.php', '_blank');
                        
                        // Reset form
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                        
                        // Show success message
                        showNotification('تم إنشاء الحساب بنجاح! سيتم توجيهك إلى الموقع الرئيسي', 'success');
                    }, 2000);
                }
            });
        }

        function validateRegisterForm(data) {
            // Check required fields
            const required = ['firstName', 'lastName', 'email', 'phone', 'password', 'confirmPassword'];
            
            for (let field of required) {
                if (!data[field] || data[field].trim() === '') {
                    showNotification(`الرجاء ملء حقل ${getFieldName(field)}`, 'error');
                    return false;
                }
            }
            
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                showNotification('الرجاء إدخال بريد إلكتروني صحيح', 'error');
                return false;
            }
            
            // Password validation
            if (data.password.length < 8) {
                showNotification('كلمة المرور يجب أن تكون 8 أحرف على الأقل', 'error');
                return false;
            }
            
            // Password confirmation
            if (data.password !== data.confirmPassword) {
                showNotification('كلمة المرور وتأكيد كلمة المرور غير متطابقتين', 'error');
                return false;
            }
            
            // Terms acceptance
            if (!data.terms) {
                showNotification('يجب الموافقة على الشروط والأحكام', 'error');
                return false;
            }
            
            return true;
        }

        function getFieldName(field) {
            const names = {
                firstName: 'الاسم الأول',
                lastName: 'الاسم الأخير',
                email: 'البريد الإلكتروني',
                phone: 'رقم الهاتف',
                password: 'كلمة المرور',
                confirmPassword: 'تأكيد كلمة المرور'
            };
            return names[field] || field;
        }

        function initializePasswordToggles() {
            const toggles = document.querySelectorAll('.password-toggle');
            
            toggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const input = this.parentElement.querySelector('input');
                    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
                    input.setAttribute('type', type);
                    
                    const icon = this.querySelector('i');
                    icon.classList.toggle('fa-eye');
                    icon.classList.toggle('fa-eye-slash');
                });
            });
        }

        function initializePasswordStrength() {
            const passwordInput = document.getElementById('password');
            const strengthIndicator = document.getElementById('passwordStrength');
            const strengthFill = strengthIndicator.querySelector('.strength-fill');
            const strengthText = strengthIndicator.querySelector('.strength-text');
            
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                
                strengthFill.style.width = strength.percentage + '%';
                strengthFill.className = `strength-fill strength-${strength.level}`;
                strengthText.textContent = strength.text;
                
                if (password.length > 0) {
                    strengthIndicator.style.display = 'block';
                } else {
                    strengthIndicator.style.display = 'none';
                }
            });
        }

        function calculatePasswordStrength(password) {
            let score = 0;
            
            if (password.length >= 8) score += 25;
            if (password.length >= 12) score += 25;
            if (/[a-z]/.test(password)) score += 12.5;
            if (/[A-Z]/.test(password)) score += 12.5;
            if (/[0-9]/.test(password)) score += 12.5;
            if (/[^A-Za-z0-9]/.test(password)) score += 12.5;
            
            if (score < 30) {
                return { level: 'weak', percentage: score, text: 'ضعيفة' };
            } else if (score < 60) {
                return { level: 'medium', percentage: score, text: 'متوسطة' };
            } else if (score < 90) {
                return { level: 'strong', percentage: score, text: 'قوية' };
            } else {
                return { level: 'very-strong', percentage: score, text: 'قوية جداً' };
            }
        }

        function initializeSocialLogin() {
            const googleBtn = document.querySelector('.btn-google');
            const facebookBtn = document.querySelector('.btn-facebook');
            
            googleBtn.addEventListener('click', function() {
                showNotification('سيتم توجيهك إلى التسجيل بجوجل', 'info');
                // Implement Google OAuth
            });
            
            facebookBtn.addEventListener('click', function() {
                showNotification('سيتم توجيهك إلى التسجيل بفيسبوك', 'info');
                // Implement Facebook OAuth
            });
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 5000);
        }
    </script>
</body>
</html>
