/* ===== Authentication Pages Styles ===== */

.auth-page {
    min-height: 100vh;
    background: var(--gradient-primary);
    overflow-x: hidden;
}

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: var(--spacing-lg);
}

.auth-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.9;
}

.auth-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

.auth-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
}

/* ===== Logo Section ===== */
.auth-logo {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    color: var(--white);
}

.auth-logo .logo {
    height: 60px;
    width: auto;
    margin-bottom: var(--spacing-md);
}

.auth-logo .brand-name {
    font-size: var(--font-size-3xl);
    font-weight: 900;
    margin-bottom: var(--spacing-sm);
    color: var(--white);
}

.auth-logo .brand-tagline {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin: 0;
}

/* ===== Form Container ===== */
.auth-form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-form-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.auth-form-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
}

.auth-form-header p {
    color: var(--gray-600);
    font-size: var(--font-size-base);
    margin: 0;
}

/* ===== Form Styles ===== */
.auth-form {
    width: 100%;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: var(--spacing-md);
    color: var(--gray-400);
    font-size: var(--font-size-base);
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-3xl) var(--spacing-md) var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
    background: var(--white);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group input::placeholder {
    color: var(--gray-400);
}

.password-toggle {
    position: absolute;
    left: var(--spacing-md);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-sm);
    z-index: 3;
    transition: var(--transition-normal);
}

.password-toggle:hover {
    color: var(--primary-color);
}

/* ===== Form Options ===== */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-sm);
    margin-left: var(--spacing-sm);
    position: relative;
    transition: var(--transition-normal);
}

.checkbox-container input:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input:checked + .checkmark::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 6px;
    width: 4px;
    height: 8px;
    border: solid var(--white);
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.forgot-link {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-normal);
}

.forgot-link:hover {
    text-decoration: underline;
}

/* ===== Buttons ===== */
.btn-full {
    width: 100%;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.divider {
    text-align: center;
    margin: var(--spacing-xl) 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gray-200);
}

.divider span {
    background: var(--white);
    padding: 0 var(--spacing-md);
    color: var(--gray-500);
    font-size: var(--font-size-sm);
    position: relative;
    z-index: 1;
}

/* ===== Social Login ===== */
.social-login {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.btn-social {
    width: 100%;
    justify-content: center;
    background: var(--white);
    border: 2px solid var(--gray-200);
    color: var(--gray-700);
    font-weight: 500;
}

.btn-social:hover {
    border-color: var(--gray-300);
    background: var(--gray-50);
}

.btn-google:hover {
    border-color: #db4437;
    color: #db4437;
}

.btn-facebook:hover {
    border-color: #4267B2;
    color: #4267B2;
}

/* ===== Auth Footer ===== */
.auth-footer {
    text-align: center;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.auth-footer a {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    margin-right: var(--spacing-sm);
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* ===== Redirect Notice ===== */
.redirect-notice {
    margin-top: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    color: var(--white);
}

.notice-content {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.notice-content i {
    font-size: var(--font-size-xl);
    color: var(--accent-color);
    margin-top: var(--spacing-xs);
}

.notice-content h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--white);
}

.notice-content p {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
    opacity: 0.9;
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* ===== Background Decorations ===== */
.auth-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    animation: float 15s ease-in-out infinite;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 15%;
    animation-delay: 5s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 20%;
    animation-delay: 10s;
}

/* ===== Footer Credit ===== */
.auth-credit {
    position: fixed;
    bottom: var(--spacing-lg);
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    z-index: 10;
}

.auth-credit a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 600;
    margin-right: var(--spacing-sm);
}

.auth-credit a:hover {
    text-decoration: underline;
}

/* ===== Notifications ===== */
.notification {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transform: translateX(100%);
    transition: var(--transition-normal);
    z-index: 1000;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-right: 4px solid var(--secondary-color);
}

.notification-error {
    border-right: 4px solid #dc2626;
}

.notification-info {
    border-right: 4px solid var(--primary-color);
}

.notification i {
    font-size: var(--font-size-lg);
}

.notification-success i {
    color: var(--secondary-color);
}

.notification-error i {
    color: #dc2626;
}

.notification-info i {
    color: var(--primary-color);
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .auth-container {
        padding: var(--spacing-md);
    }
    
    .auth-content {
        max-width: 100%;
    }
    
    .auth-form-container {
        padding: var(--spacing-xl);
    }
    
    .auth-logo .brand-name {
        font-size: var(--font-size-2xl);
    }
    
    .auth-logo .brand-tagline {
        font-size: var(--font-size-base);
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .social-login {
        gap: var(--spacing-sm);
    }
    
    .notice-content {
        flex-direction: column;
        text-align: center;
    }
    
    .auth-credit {
        position: relative;
        bottom: auto;
        left: auto;
        transform: none;
        margin-top: var(--spacing-xl);
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .auth-container {
        padding: var(--spacing-sm);
    }
    
    .auth-form-container {
        padding: var(--spacing-lg);
    }
    
    .auth-logo .logo {
        height: 50px;
    }
    
    .auth-logo .brand-name {
        font-size: var(--font-size-xl);
    }
    
    .input-group input {
        padding: var(--spacing-sm) var(--spacing-2xl) var(--spacing-sm) var(--spacing-sm);
    }
    
    .notification {
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        max-width: none;
    }
}
