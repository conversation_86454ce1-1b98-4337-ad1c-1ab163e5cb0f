# دليل رفع موقع نقرة هوست
# Nakra Host Deployment Guide

## 📋 قائمة المراجعة قبل الرفع

### ✅ الملفات المطلوبة
- [x] index.php (الصفحة الرئيسية)
- [x] contact.php (معالج نموذج الاتصال)
- [x] .htaccess (إعدادات Apache)
- [x] manifest.json (ملف PWA)
- [x] sw.js (Service Worker)
- [x] مجلد css/ (جميع ملفات الأنماط)
- [x] مجلد js/ (جميع ملفات JavaScript)
- [x] مجلد images/ (الصور - يحتاج إضافة الصور الفعلية)

### 🔧 الإعدادات المطلوبة

#### 1. تعديل معلومات الاتصال في contact.php
```php
$to_email = '<EMAIL>';        // غير إلى بريدك الفعلي
$from_email = '<EMAIL>';   // غير إلى نطاقك
$company_name = 'نقرة هوست';
```

#### 2. تعديل الروابط في index.php
```php
$main_site = "https://hostmeed.cloud";           // الموقع الرئيسي
$marketing_site = "https://nakraformarketing.com"; // موقع التسويق
$contact_phone = "+201028351237";                // رقم الهاتف
$contact_email = "<EMAIL>";           // البريد الإلكتروني
```

## 🚀 خطوات الرفع على الاستضافة

### الخطوة 1: تحضير الملفات
1. تأكد من وجود جميع الملفات
2. اضغط الملفات في ملف ZIP
3. تأكد من أن ملف .htaccess مضمن

### الخطوة 2: رفع الملفات
1. ادخل إلى لوحة تحكم الاستضافة (cPanel)
2. اذهب إلى File Manager
3. ادخل إلى مجلد public_html
4. ارفع ملف ZIP واستخرجه
5. أو ارفع الملفات مباشرة

### الخطوة 3: ضبط الصلاحيات
```bash
# صلاحيات المجلدات
chmod 755 css/
chmod 755 js/
chmod 755 images/

# صلاحيات الملفات
chmod 644 *.php
chmod 644 *.html
chmod 644 css/*.css
chmod 644 js/*.js
chmod 644 .htaccess
```

### الخطوة 4: اختبار الموقع
1. ادخل إلى رابط موقعك
2. تأكد من ظهور الصفحة بشكل صحيح
3. اختبر نموذج الاتصال
4. تأكد من عمل تبديل اللغات
5. اختبر الموقع على الهاتف المحمول

## 🔍 استكشاف الأخطاء

### مشكلة: الصفحة لا تظهر
- تأكد من أن index.php في المجلد الصحيح
- تحقق من صلاحيات الملفات
- راجع error logs في cPanel

### مشكلة: نموذج الاتصال لا يعمل
- تأكد من أن PHP مفعل
- تحقق من إعدادات البريد الإلكتروني
- راجع ملف contact.php للأخطاء

### مشكلة: الصور لا تظهر
- تأكد من رفع مجلد images/
- أضف الصور المطلوبة:
  - logo.png (شعار الشركة)
  - about-team.jpg (صورة الفريق)
  - أيقونات PWA بأحجام مختلفة

### مشكلة: الخطوط العربية لا تظهر
- تأكد من اتصال الإنترنت لتحميل Google Fonts
- تحقق من إعدادات encoding في الخادم

## 📱 إعداد PWA (اختياري)

### إضافة الأيقونات
أضف الأيقونات التالية في مجلد images/:
- icon-72x72.png
- icon-96x96.png
- icon-128x128.png
- icon-144x144.png
- icon-152x152.png
- icon-192x192.png
- icon-384x384.png
- icon-512x512.png

### تفعيل HTTPS
- احصل على شهادة SSL
- فعل إعادة التوجيه من HTTP إلى HTTPS

## 🎯 تحسين الأداء

### ضغط الملفات
- تأكد من تفعيل Gzip في .htaccess
- استخدم أدوات ضغط CSS و JavaScript

### تحسين الصور
- اضغط الصور قبل الرفع
- استخدم تنسيق WebP إذا أمكن
- أضف lazy loading للصور

### تحسين محركات البحث
- أضف Google Analytics
- أضف Google Search Console
- تأكد من صحة البيانات المنظمة

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. راجع error logs في cPanel
2. تأكد من متطلبات الاستضافة
3. تواصل مع فريق الدعم الفني للاستضافة

## ✅ قائمة المراجعة النهائية

- [ ] رفع جميع الملفات
- [ ] ضبط الصلاحيات
- [ ] تعديل معلومات الاتصال
- [ ] اختبار نموذج الاتصال
- [ ] اختبار تبديل اللغات
- [ ] اختبار الموقع على الهاتف
- [ ] إضافة الصور المطلوبة
- [ ] تفعيل SSL
- [ ] إعداد Google Analytics
- [ ] اختبار سرعة الموقع

---

**تم إنشاء هذا الموقع بواسطة فريق نقرة للتسويق الإلكتروني**
**Website created by Nakra Digital Marketing Team**
