/* ===== CSS Fixes for Professional Design ===== */

/* Fix spacing and layout issues */
body {
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* Header fixes */
.header {
    background: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
    position: relative;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Language switcher positioning */
.language-switcher {
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    z-index: 1001;
}

.lang-btn {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.lang-btn:hover {
    background: #f9fafb;
    border-color: #2563eb;
    color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Brand styling */
.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
}

.logo {
    height: 45px;
    width: auto;
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    font-family: 'Cairo', sans-serif;
}

/* Navigation menu */
.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: #374151;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-link:hover,
.nav-link.active {
    color: #2563eb;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Action buttons */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Button improvements */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: #2563eb;
    color: #ffffff;
    box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.35);
    color: #ffffff;
}

.btn-outline {
    background: transparent;
    color: #2563eb;
    border: 2px solid #2563eb;
}

.btn-outline:hover {
    background: #2563eb;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.25);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Hero section fixes */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: calc(80px + 4rem) 0 4rem;
    margin-top: 0;
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

/* Section spacing fixes */
.section {
    padding: 5rem 0;
    margin: 2rem 0;
}

.section-header {
    margin-bottom: 4rem;
    padding: 0 1.5rem;
    text-align: center;
}

.section-title {
    font-size: 2.25rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    color: #111827;
}

.section-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Services grid fixes */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
    padding: 0 1.5rem;
}

.service-card {
    text-align: center;
    transition: all 0.3s ease;
    padding: 3rem;
    margin-bottom: 1.5rem;
    background: #ffffff;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: #2563eb;
}

/* Icon improvements */
.icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 0.75rem;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.icon-primary {
    background: #2563eb;
    color: #ffffff;
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.25);
}

.icon-secondary {
    background: #10b981;
    color: #ffffff;
    box-shadow: 0 8px 25px 0 rgba(16, 185, 129, 0.25);
}

.icon-accent {
    background: #f59e0b;
    color: #ffffff;
    box-shadow: 0 8px 25px 0 rgba(245, 158, 11, 0.25);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .navbar .container {
        padding: 0 1rem;
    }
    
    .language-switcher {
        top: 0.25rem;
        right: 0.5rem;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
    }
    
    .service-card {
        padding: 2rem;
    }
    
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
        flex-direction: column;
        gap: 4px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
    }
    
    .mobile-menu-toggle span {
        width: 25px;
        height: 3px;
        background: #374151;
        border-radius: 2px;
        transition: all 0.3s ease;
    }
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 1.5rem;
    left: 1.5rem;
    width: 50px;
    height: 50px;
    background: #2563eb;
    border: none;
    border-radius: 50%;
    color: #ffffff;
    font-size: 1.125rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.25);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px 0 rgba(37, 99, 235, 0.35);
}

/* Smooth animations */
* {
    scroll-behavior: smooth;
}

/* Loading state */
body.loaded {
    opacity: 1;
}

/* Utility classes */
.text-center {
    text-align: center;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}
