// ===== Authentication JavaScript =====

class AuthManager {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupFormValidation();
        this.setupPasswordStrength();
        this.setupPasswordToggles();
        this.setupSocialAuth();
        this.setupFormSubmission();
    }
    
    // ===== Form Validation =====
    setupFormValidation() {
        const forms = document.querySelectorAll('.auth-form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input[required]');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearFieldError(input));
            });
        });
    }
    
    validateField(field) {
        const value = field.value.trim();
        const fieldType = field.type;
        const fieldName = field.name;
        
        // Clear previous errors
        this.clearFieldError(field);
        
        // Required field validation
        if (field.hasAttribute('required') && !value) {
            this.showFieldError(field, this.getErrorMessage('required', fieldName));
            return false;
        }
        
        // Email validation
        if (fieldType === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                this.showFieldError(field, this.getErrorMessage('email'));
                return false;
            }
        }
        
        // Phone validation
        if (fieldType === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
            if (!phoneRegex.test(value)) {
                this.showFieldError(field, this.getErrorMessage('phone'));
                return false;
            }
        }
        
        // Password validation
        if (fieldType === 'password' && value) {
            if (value.length < 8) {
                this.showFieldError(field, this.getErrorMessage('password_length'));
                return false;
            }
        }
        
        // Confirm password validation
        if (fieldName === 'confirmPassword' && value) {
            const passwordField = document.querySelector('input[name="password"]');
            if (passwordField && value !== passwordField.value) {
                this.showFieldError(field, this.getErrorMessage('password_match'));
                return false;
            }
        }
        
        return true;
    }
    
    showFieldError(field, message) {
        field.classList.add('error');
        
        // Remove existing error message
        const existingError = field.parentElement.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        // Add new error message
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        field.parentElement.appendChild(errorElement);
    }
    
    clearFieldError(field) {
        field.classList.remove('error');
        const errorElement = field.parentElement.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }
    
    getErrorMessage(type, fieldName = '') {
        const messages = {
            required: `الرجاء ملء حقل ${this.getFieldDisplayName(fieldName)}`,
            email: 'الرجاء إدخال بريد إلكتروني صحيح',
            phone: 'الرجاء إدخال رقم هاتف صحيح',
            password_length: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
            password_match: 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين'
        };
        
        return messages[type] || 'خطأ في البيانات المدخلة';
    }
    
    getFieldDisplayName(fieldName) {
        const names = {
            firstName: 'الاسم الأول',
            lastName: 'الاسم الأخير',
            email: 'البريد الإلكتروني',
            phone: 'رقم الهاتف',
            password: 'كلمة المرور',
            confirmPassword: 'تأكيد كلمة المرور',
            company: 'اسم الشركة'
        };
        
        return names[fieldName] || fieldName;
    }
    
    // ===== Password Strength =====
    setupPasswordStrength() {
        const passwordInputs = document.querySelectorAll('input[name="password"]');
        
        passwordInputs.forEach(input => {
            const strengthIndicator = input.parentElement.parentElement.querySelector('.password-strength');
            
            if (strengthIndicator) {
                input.addEventListener('input', () => {
                    this.updatePasswordStrength(input, strengthIndicator);
                });
            }
        });
    }
    
    updatePasswordStrength(input, indicator) {
        const password = input.value;
        const strength = this.calculatePasswordStrength(password);
        
        const strengthFill = indicator.querySelector('.strength-fill');
        const strengthText = indicator.querySelector('.strength-text');
        
        if (password.length > 0) {
            indicator.style.display = 'block';
            strengthFill.style.width = strength.percentage + '%';
            strengthFill.className = `strength-fill strength-${strength.level}`;
            strengthText.textContent = strength.text;
        } else {
            indicator.style.display = 'none';
        }
    }
    
    calculatePasswordStrength(password) {
        let score = 0;
        
        // Length
        if (password.length >= 8) score += 25;
        if (password.length >= 12) score += 25;
        
        // Character types
        if (/[a-z]/.test(password)) score += 12.5;
        if (/[A-Z]/.test(password)) score += 12.5;
        if (/[0-9]/.test(password)) score += 12.5;
        if (/[^A-Za-z0-9]/.test(password)) score += 12.5;
        
        if (score < 30) {
            return { level: 'weak', percentage: score, text: 'ضعيفة' };
        } else if (score < 60) {
            return { level: 'medium', percentage: score, text: 'متوسطة' };
        } else if (score < 90) {
            return { level: 'strong', percentage: score, text: 'قوية' };
        } else {
            return { level: 'very-strong', percentage: score, text: 'قوية جداً' };
        }
    }
    
    // ===== Password Toggles =====
    setupPasswordToggles() {
        const toggleButtons = document.querySelectorAll('.password-toggle');
        
        toggleButtons.forEach(button => {
            button.addEventListener('click', () => {
                const input = button.parentElement.querySelector('input');
                const icon = button.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
    }
    
    // ===== Social Authentication =====
    setupSocialAuth() {
        const googleBtns = document.querySelectorAll('.btn-google');
        const facebookBtns = document.querySelectorAll('.btn-facebook');
        
        googleBtns.forEach(btn => {
            btn.addEventListener('click', () => this.handleGoogleAuth());
        });
        
        facebookBtns.forEach(btn => {
            btn.addEventListener('click', () => this.handleFacebookAuth());
        });
    }
    
    handleGoogleAuth() {
        this.showNotification('سيتم توجيهك إلى تسجيل الدخول بجوجل', 'info');
        // In a real application, implement Google OAuth here
        console.log('Google OAuth would be implemented here');
    }
    
    handleFacebookAuth() {
        this.showNotification('سيتم توجيهك إلى تسجيل الدخول بفيسبوك', 'info');
        // In a real application, implement Facebook OAuth here
        console.log('Facebook OAuth would be implemented here');
    }
    
    // ===== Form Submission =====
    setupFormSubmission() {
        const forms = document.querySelectorAll('.auth-form');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmission(form);
            });
        });
    }
    
    handleFormSubmission(form) {
        // Validate all fields
        const inputs = form.querySelectorAll('input[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        // Check terms acceptance for register form
        const termsCheckbox = form.querySelector('input[name="terms"]');
        if (termsCheckbox && !termsCheckbox.checked) {
            this.showNotification('يجب الموافقة على الشروط والأحكام', 'error');
            isValid = false;
        }
        
        if (!isValid) {
            this.showNotification('الرجاء تصحيح الأخطاء في النموذج', 'error');
            return;
        }
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        this.setButtonLoading(submitBtn, true);
        
        // Get form data
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // Simulate API call
        setTimeout(() => {
            this.processFormData(form, data);
            this.setButtonLoading(submitBtn, false);
        }, 2000);
    }
    
    processFormData(form, data) {
        const isLoginForm = form.id === 'loginForm';
        const isRegisterForm = form.id === 'registerForm';
        
        if (isLoginForm) {
            this.handleLogin(data);
        } else if (isRegisterForm) {
            this.handleRegister(data);
        }
    }
    
    handleLogin(data) {
        console.log('Login data:', data);
        
        // In a real application, send data to server
        // For demo, redirect to hostmeed.cloud
        window.open('https://hostmeed.cloud/clientarea.php', '_blank');
        this.showNotification('تم تسجيل الدخول بنجاح', 'success');
    }
    
    handleRegister(data) {
        console.log('Register data:', data);
        
        // In a real application, send data to server
        // For demo, redirect to hostmeed.cloud
        window.open('https://hostmeed.cloud/register.php', '_blank');
        this.showNotification('تم إنشاء الحساب بنجاح! سيتم توجيهك إلى الموقع الرئيسي', 'success');
    }
    
    // ===== UI Helpers =====
    setButtonLoading(button, isLoading) {
        if (isLoading) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري المعالجة...</span>';
        } else {
            button.disabled = false;
            // Restore original text based on button context
            const isLogin = button.closest('#loginForm');
            const isRegister = button.closest('#registerForm');
            
            if (isLogin) {
                button.innerHTML = '<span data-ar="تسجيل الدخول" data-en="Login">تسجيل الدخول</span> <i class="fas fa-arrow-left"></i>';
            } else if (isRegister) {
                button.innerHTML = '<span data-ar="إنشاء الحساب" data-en="Create Account">إنشاء الحساب</span> <i class="fas fa-arrow-left"></i>';
            }
        }
    }
    
    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const iconClass = type === 'success' ? 'check-circle' : 
                         type === 'error' ? 'exclamation-circle' : 'info-circle';
        
        notification.innerHTML = `
            <i class="fas fa-${iconClass}"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // Hide notification after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
}

// Initialize auth manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.authManager = new AuthManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
