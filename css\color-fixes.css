/* ===== Color and Contrast Fixes ===== */

/* Global text improvements */
body {
    color: #374151 !important;
    font-weight: 400;
}

/* Header text improvements */
.header {
    background: #ffffff !important;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.brand-name {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 900 !important;
}

/* Navigation improvements */
.nav-link {
    color: #1f2937 !important;
    font-weight: 600 !important;
}

.nav-link:hover,
.nav-link.active {
    color: #2563eb !important;
}

/* Language button improvements */
.lang-btn {
    background: #ffffff !important;
    color: #374151 !important;
    border: 1px solid #d1d5db !important;
    font-weight: 600 !important;
}

.lang-btn:hover {
    background: #f9fafb !important;
    color: #2563eb !important;
    border-color: #2563eb !important;
}

/* Hero section text fixes */
.hero {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%) !important;
}

.hero-title {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-title .gradient-text {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-shadow: none !important;
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.hero-badge {
    background: rgba(255, 255, 255, 0.15) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Hero stats text */
.stat-number {
    color: #fbbf24 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Hero cards text */
.floating-card {
    background: rgba(255, 255, 255, 0.95) !important;
    color: #1f2937 !important;
}

.floating-card h4 {
    color: #1f2937 !important;
    font-weight: 700 !important;
}

.floating-card p {
    color: #6b7280 !important;
}

/* Services section improvements */
.services {
    background: #f8fafc !important;
}

.section-title {
    color: #111827 !important;
    font-weight: 900 !important;
}

.section-subtitle {
    color: #4b5563 !important;
    font-weight: 500 !important;
}

.section-badge {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
}

/* Service cards text */
.service-card h3 {
    color: #111827 !important;
    font-weight: 700 !important;
}

.service-card p {
    color: #4b5563 !important;
    font-weight: 400 !important;
}

.feature-item {
    color: #374151 !important;
    font-weight: 500 !important;
}

.feature-item i {
    font-weight: 900 !important;
}

/* Pricing section improvements */
.pricing {
    background: #f8fafc !important;
}

.pricing .section-title {
    color: #111827 !important;
    font-weight: 900 !important;
}

.pricing .section-subtitle {
    color: #4b5563 !important;
    font-weight: 500 !important;
}

.pricing .section-badge {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
}

.pricing-card {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    color: #1f2937 !important;
}

.pricing-card.featured {
    border-color: #2563eb !important;
    background: #ffffff !important;
}

.popular-badge {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
}

.plan-name {
    color: #111827 !important;
    font-weight: 700 !important;
}

.price-monthly .amount {
    color: #2563eb !important;
    font-weight: 900 !important;
}

.price-monthly .currency,
.price-monthly .period {
    color: #6b7280 !important;
    font-weight: 600 !important;
}

.price-yearly {
    color: #6b7280 !important;
    font-weight: 500 !important;
}

.pricing-features .feature-item {
    color: #374151 !important;
    font-weight: 500 !important;
}

.pricing-features .feature-item i {
    color: #10b981 !important;
}

.guarantee-text {
    color: #6b7280 !important;
    font-weight: 500 !important;
}

.guarantee-text i {
    color: #10b981 !important;
}

/* Additional services pricing */
.additional-services .services-title {
    color: #111827 !important;
    font-weight: 700 !important;
}

.service-price-card {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
}

.service-price-card h4 {
    color: #111827 !important;
    font-weight: 700 !important;
}

.service-price {
    color: #4b5563 !important;
}

.service-price .price {
    color: #2563eb !important;
    font-weight: 900 !important;
}

/* Contact section improvements */
.contact {
    background: #f8fafc !important;
}

.contact .section-title {
    color: #111827 !important;
    font-weight: 900 !important;
}

.contact .section-subtitle {
    color: #4b5563 !important;
    font-weight: 500 !important;
}

.contact .section-badge {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-card {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 1rem !important;
    padding: 2rem !important;
    text-align: center;
    transition: all 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
    border-color: #2563eb !important;
}

.contact-card .icon {
    margin-bottom: 1rem;
}

.contact-card h4 {
    color: #111827 !important;
    font-weight: 700 !important;
    margin-bottom: 0.75rem;
    font-size: 1.125rem;
}

.contact-card p {
    color: #4b5563 !important;
    font-weight: 500 !important;
    margin-bottom: 1.25rem;
    font-size: 1rem;
}

.contact-card .btn {
    background: transparent !important;
    color: #2563eb !important;
    border: 2px solid #2563eb !important;
    font-weight: 600 !important;
}

.contact-card .btn:hover {
    background: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-2px);
}

/* Contact form improvements */
.contact-form-wrapper {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 1.5rem !important;
    padding: 2.5rem !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08) !important;
}

.contact-form h3 {
    color: #111827 !important;
    font-weight: 700 !important;
    margin-bottom: 1.5rem;
    text-align: center;
}

/* Form improvements */
.form-group label {
    color: #374151 !important;
    font-weight: 600 !important;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    display: block;
}

.form-group input,
.form-group textarea {
    color: #1f2937 !important;
    border: 2px solid #d1d5db !important;
    background: #ffffff !important;
    border-radius: 0.5rem !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #2563eb !important;
    color: #1f2937 !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
    outline: none !important;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af !important;
    font-weight: 400 !important;
}

.contact-form .btn {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
    border: none !important;
    padding: 1rem 2rem !important;
    font-size: 1rem !important;
    font-weight: 700 !important;
    border-radius: 0.75rem !important;
    width: 100% !important;
    margin-top: 1rem !important;
}

.contact-form .btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.25) !important;
}

/* Hero cards z-index and positioning fixes */
.hero-visual .hero-cards {
    position: relative;
    z-index: 10;
}

.floating-card {
    position: relative;
    z-index: 11;
    isolation: isolate;
}

/* Language switcher improvements */
.language-switcher {
    position: fixed !important;
    top: 1rem !important;
    right: 1.5rem !important;
    z-index: 9999 !important;
}

/* Prevent overlapping issues */
.hero-content > * {
    position: relative;
    z-index: 5;
}

.hero-stats {
    position: relative;
    z-index: 6;
}

/* Better spacing for hero elements */
.hero-text {
    padding-right: 2rem;
}

.hero-visual {
    padding-left: 2rem;
}

/* Footer improvements */
.footer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
    color: #ffffff !important;
}

.footer-section h3,
.footer-section h4 {
    color: #ffffff !important;
    font-weight: 700 !important;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 400 !important;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

.footer-links a:hover {
    color: #60a5fa !important;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7) !important;
}

.footer-bottom a {
    color: #60a5fa !important;
    font-weight: 600 !important;
}

.contact-item {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

.contact-item i {
    color: #60a5fa !important;
}

/* Button text improvements */
.btn {
    font-weight: 700 !important;
}

.btn-primary {
    color: #ffffff !important;
}

.btn-secondary {
    color: #ffffff !important;
}

.btn-accent {
    color: #ffffff !important;
}

.btn-outline {
    color: #2563eb !important;
}

.btn-outline:hover {
    color: #ffffff !important;
}

/* Back to top button */
.back-to-top {
    background: #2563eb !important;
    color: #ffffff !important;
}

/* Scroll indicator */
.scroll-arrow {
    color: rgba(255, 255, 255, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

.scroll-arrow:hover {
    color: #ffffff !important;
    border-color: #ffffff !important;
}

/* Mobile menu improvements */
.mobile-menu-toggle span {
    background: #374151 !important;
}

/* Ensure all text is readable */
* {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* High contrast for accessibility */
@media (prefers-contrast: high) {
    .hero-title .gradient-text {
        -webkit-text-fill-color: #fbbf24 !important;
    }
    
    .brand-name {
        -webkit-text-fill-color: #2563eb !important;
    }
}

/* Dark mode text fixes */
@media (prefers-color-scheme: dark) {
    .section-title {
        color: #f9fafb !important;
    }
    
    .section-subtitle {
        color: #d1d5db !important;
    }
    
    .service-card h3 {
        color: #f9fafb !important;
    }
    
    .service-card p {
        color: #d1d5db !important;
    }
}
