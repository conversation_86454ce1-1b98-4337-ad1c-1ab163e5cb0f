/* ===== Color and Contrast Fixes ===== */

/* Global text improvements */
body {
    color: #374151 !important;
    font-weight: 400;
}

/* Header text improvements */
.header {
    background: #ffffff !important;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.brand-name {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 900 !important;
}

/* Navigation improvements */
.nav-link {
    color: #1f2937 !important;
    font-weight: 600 !important;
}

.nav-link:hover,
.nav-link.active {
    color: #2563eb !important;
}

/* Language button improvements */
.lang-btn {
    background: #ffffff !important;
    color: #374151 !important;
    border: 1px solid #d1d5db !important;
    font-weight: 600 !important;
}

.lang-btn:hover {
    background: #f9fafb !important;
    color: #2563eb !important;
    border-color: #2563eb !important;
}

/* Hero section text fixes */
.hero {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%) !important;
}

.hero-title {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-title .gradient-text {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-shadow: none !important;
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.hero-badge {
    background: rgba(255, 255, 255, 0.15) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Hero stats text */
.stat-number {
    color: #fbbf24 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Hero cards text */
.floating-card {
    background: rgba(255, 255, 255, 0.95) !important;
    color: #1f2937 !important;
}

.floating-card h4 {
    color: #1f2937 !important;
    font-weight: 700 !important;
}

.floating-card p {
    color: #6b7280 !important;
}

/* Services section improvements */
.services {
    background: #f8fafc !important;
}

.section-title {
    color: #111827 !important;
    font-weight: 900 !important;
}

.section-subtitle {
    color: #4b5563 !important;
    font-weight: 500 !important;
}

.section-badge {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: #ffffff !important;
}

/* Service cards text */
.service-card h3 {
    color: #111827 !important;
    font-weight: 700 !important;
}

.service-card p {
    color: #4b5563 !important;
    font-weight: 400 !important;
}

.feature-item {
    color: #374151 !important;
    font-weight: 500 !important;
}

.feature-item i {
    font-weight: 900 !important;
}

/* Pricing section */
.pricing {
    background: #ffffff !important;
}

.plan-name {
    color: #111827 !important;
    font-weight: 700 !important;
}

.price-monthly .amount {
    color: #2563eb !important;
    font-weight: 900 !important;
}

.price-monthly .currency,
.price-monthly .period {
    color: #6b7280 !important;
    font-weight: 600 !important;
}

.price-yearly {
    color: #6b7280 !important;
    font-weight: 500 !important;
}

/* Contact section */
.contact {
    background: #f8fafc !important;
}

.contact-card h4 {
    color: #111827 !important;
    font-weight: 700 !important;
}

.contact-card p {
    color: #4b5563 !important;
    font-weight: 500 !important;
}

/* Form improvements */
.form-group label {
    color: #374151 !important;
    font-weight: 600 !important;
}

.form-group input,
.form-group textarea {
    color: #1f2937 !important;
    border: 2px solid #d1d5db !important;
    background: #ffffff !important;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #2563eb !important;
    color: #1f2937 !important;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af !important;
}

/* Footer improvements */
.footer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
    color: #ffffff !important;
}

.footer-section h3,
.footer-section h4 {
    color: #ffffff !important;
    font-weight: 700 !important;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 400 !important;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

.footer-links a:hover {
    color: #60a5fa !important;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7) !important;
}

.footer-bottom a {
    color: #60a5fa !important;
    font-weight: 600 !important;
}

.contact-item {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

.contact-item i {
    color: #60a5fa !important;
}

/* Button text improvements */
.btn {
    font-weight: 700 !important;
}

.btn-primary {
    color: #ffffff !important;
}

.btn-secondary {
    color: #ffffff !important;
}

.btn-accent {
    color: #ffffff !important;
}

.btn-outline {
    color: #2563eb !important;
}

.btn-outline:hover {
    color: #ffffff !important;
}

/* Back to top button */
.back-to-top {
    background: #2563eb !important;
    color: #ffffff !important;
}

/* Scroll indicator */
.scroll-arrow {
    color: rgba(255, 255, 255, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

.scroll-arrow:hover {
    color: #ffffff !important;
    border-color: #ffffff !important;
}

/* Mobile menu improvements */
.mobile-menu-toggle span {
    background: #374151 !important;
}

/* Ensure all text is readable */
* {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* High contrast for accessibility */
@media (prefers-contrast: high) {
    .hero-title .gradient-text {
        -webkit-text-fill-color: #fbbf24 !important;
    }
    
    .brand-name {
        -webkit-text-fill-color: #2563eb !important;
    }
}

/* Dark mode text fixes */
@media (prefers-color-scheme: dark) {
    .section-title {
        color: #f9fafb !important;
    }
    
    .section-subtitle {
        color: #d1d5db !important;
    }
    
    .service-card h3 {
        color: #f9fafb !important;
    }
    
    .service-card p {
        color: #d1d5db !important;
    }
}
