<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نقرة هوست</title>
    <meta name="description" content="تسجيل الدخول إلى حسابك في نقرة هوست">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body class="auth-page">
    <!-- Language Switcher -->
    <div class="language-switcher">
        <button id="langToggle" class="lang-btn">
            <i class="fas fa-globe"></i>
            <span class="lang-text">English</span>
        </button>
    </div>

    <!-- Auth Container -->
    <div class="auth-container">
        <div class="auth-background">
            <div class="auth-particles"></div>
        </div>
        
        <div class="auth-content">
            <!-- Logo Section -->
            <div class="auth-logo">
                <img src="images/logo.png" alt="نقرة هوست" class="logo">
                <h1 class="brand-name" data-ar="نقرة هوست" data-en="Nakra Host">نقرة هوست</h1>
                <p class="brand-tagline" data-ar="حلولك الرقمية المتكاملة" data-en="Your Complete Digital Solutions">حلولك الرقمية المتكاملة</p>
            </div>

            <!-- Login Form -->
            <div class="auth-form-container animate-fade-up">
                <div class="auth-form-header">
                    <h2 data-ar="تسجيل الدخول" data-en="Login">تسجيل الدخول</h2>
                    <p data-ar="مرحباً بك مرة أخرى! يرجى تسجيل الدخول إلى حسابك" 
                       data-en="Welcome back! Please login to your account">
                        مرحباً بك مرة أخرى! يرجى تسجيل الدخول إلى حسابك
                    </p>
                </div>

                <form class="auth-form" id="loginForm">
                    <div class="form-group">
                        <label for="email" data-ar="البريد الإلكتروني" data-en="Email Address">البريد الإلكتروني</label>
                        <div class="input-group">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="email" name="email" required 
                                   placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" data-ar="كلمة المرور" data-en="Password">كلمة المرور</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" required 
                                   placeholder="••••••••">
                            <button type="button" class="password-toggle" id="passwordToggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="remember" name="remember">
                            <span class="checkmark"></span>
                            <span data-ar="تذكرني" data-en="Remember me">تذكرني</span>
                        </label>
                        <a href="forgot-password.html" class="forgot-link" 
                           data-ar="نسيت كلمة المرور؟" data-en="Forgot Password?">نسيت كلمة المرور؟</a>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full">
                        <span data-ar="تسجيل الدخول" data-en="Login">تسجيل الدخول</span>
                        <i class="fas fa-arrow-left"></i>
                    </button>

                    <div class="divider">
                        <span data-ar="أو" data-en="or">أو</span>
                    </div>

                    <div class="social-login">
                        <button type="button" class="btn btn-social btn-google">
                            <i class="fab fa-google"></i>
                            <span data-ar="تسجيل الدخول بجوجل" data-en="Login with Google">تسجيل الدخول بجوجل</span>
                        </button>
                        <button type="button" class="btn btn-social btn-facebook">
                            <i class="fab fa-facebook-f"></i>
                            <span data-ar="تسجيل الدخول بفيسبوك" data-en="Login with Facebook">تسجيل الدخول بفيسبوك</span>
                        </button>
                    </div>

                    <div class="auth-footer">
                        <p>
                            <span data-ar="ليس لديك حساب؟" data-en="Don't have an account?">ليس لديك حساب؟</span>
                            <a href="register.html" data-ar="إنشاء حساب جديد" data-en="Create new account">إنشاء حساب جديد</a>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Redirect to Main Site -->
            <div class="redirect-notice animate-fade-up" data-delay="200">
                <div class="notice-content">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <h4 data-ar="الدخول إلى لوحة التحكم" data-en="Access Control Panel">الدخول إلى لوحة التحكم</h4>
                        <p data-ar="سيتم توجيهك إلى موقعنا الرئيسي" data-en="You will be redirected to our main site">
                            سيتم توجيهك إلى موقعنا الرئيسي
                        </p>
                        <a href="https://hostmeed.cloud/clientarea.php" class="btn btn-outline btn-sm" target="_blank">
                            <span data-ar="الذهاب إلى hostmeed.cloud" data-en="Go to hostmeed.cloud">الذهاب إلى hostmeed.cloud</span>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Background Elements -->
        <div class="auth-decorations">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>
    </div>

    <!-- Footer Credit -->
    <footer class="auth-credit">
        <p>
            <span data-ar="تم إنشاء الموقع بواسطة فريق" data-en="Website created by">تم إنشاء الموقع بواسطة فريق</span>
            <a href="https://nakraformarketing.com" target="_blank">نقرة للتسويق الإلكتروني</a>
        </p>
    </footer>

    <!-- Scripts -->
    <script src="js/language.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize login form
        document.addEventListener('DOMContentLoaded', function() {
            initializeLoginForm();
            initializePasswordToggle();
            initializeSocialLogin();
        });

        function initializeLoginForm() {
            const loginForm = document.getElementById('loginForm');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const email = formData.get('email');
                const password = formData.get('password');
                const remember = formData.get('remember');
                
                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span data-ar="جاري تسجيل الدخول..." data-en="Logging in...">جاري تسجيل الدخول...</span>';
                submitBtn.disabled = true;
                
                // Simulate login process
                setTimeout(() => {
                    // In a real application, you would send this data to your server
                    console.log('Login attempt:', { email, password, remember });
                    
                    // Redirect to hostmeed.cloud
                    window.open('https://hostmeed.cloud/clientarea.php', '_blank');
                    
                    // Reset form
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    
                    // Show success message
                    showNotification('تم تسجيل الدخول بنجاح', 'success');
                }, 2000);
            });
        }

        function initializePasswordToggle() {
            const passwordToggle = document.getElementById('passwordToggle');
            const passwordInput = document.getElementById('password');
            
            passwordToggle.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
        }

        function initializeSocialLogin() {
            const googleBtn = document.querySelector('.btn-google');
            const facebookBtn = document.querySelector('.btn-facebook');
            
            googleBtn.addEventListener('click', function() {
                showNotification('سيتم توجيهك إلى تسجيل الدخول بجوجل', 'info');
                // Implement Google OAuth
            });
            
            facebookBtn.addEventListener('click', function() {
                showNotification('سيتم توجيهك إلى تسجيل الدخول بفيسبوك', 'info');
                // Implement Facebook OAuth
            });
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
