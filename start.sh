#!/bin/bash

# نقرة هوست - خادم محلي
# Nakra Host Local Development Server

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    echo -e "${1}${2}${NC}"
}

# Function to print banner
print_banner() {
    echo
    print_colored $CYAN "========================================"
    print_colored $CYAN "🚀 نقرة هوست - خادم محلي"
    print_colored $CYAN "   Nakra Host Local Development Server"
    print_colored $CYAN "========================================"
    echo
}

# Function to check requirements
check_requirements() {
    # Check if Python is installed
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        print_colored $RED "❌ Python غير مثبت على النظام"
        print_colored $YELLOW "💡 يرجى تثبيت Python من: https://python.org"
        echo
        exit 1
    fi
    
    # Check if required files exist
    if [ ! -f "index.html" ]; then
        print_colored $RED "❌ ملف index.html غير موجود"
        print_colored $YELLOW "💡 تأكد من وجودك في مجلد المشروع الصحيح"
        echo
        exit 1
    fi
    
    print_colored $GREEN "✅ تم العثور على Python"
    print_colored $GREEN "✅ تم العثور على ملفات المشروع"
    echo
}

# Function to start server
start_server() {
    print_colored $BLUE "🔄 بدء تشغيل الخادم..."
    print_colored $BLUE "📍 العنوان: http://localhost:8000"
    print_colored $YELLOW "💡 اضغط Ctrl+C لإيقاف الخادم"
    echo
    
    # Try python3 first, then python
    if command -v python3 &> /dev/null; then
        python3 server.py
    else
        python server.py
    fi
    
    echo
    print_colored $PURPLE "👋 تم إيقاف الخادم"
}

# Function to show help
show_help() {
    echo "الاستخدام: $0 [OPTIONS]"
    echo
    echo "الخيارات:"
    echo "  -h, --help     عرض هذه الرسالة"
    echo "  -p, --port     تحديد رقم المنفذ (افتراضي: 8000)"
    echo "  -o, --open     فتح المتصفح تلقائياً"
    echo
    echo "أمثلة:"
    echo "  $0              # تشغيل على المنفذ 8000"
    echo "  $0 -p 3000      # تشغيل على المنفذ 3000"
    echo "  $0 -o           # تشغيل وفتح المتصفح"
    echo
}

# Parse command line arguments
PORT=8000
OPEN_BROWSER=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -o|--open)
            OPEN_BROWSER=true
            shift
            ;;
        *)
            print_colored $RED "خيار غير معروف: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_banner
    check_requirements
    
    if [ "$OPEN_BROWSER" = true ]; then
        # Open browser in background
        (sleep 3 && open "http://localhost:$PORT" 2>/dev/null || xdg-open "http://localhost:$PORT" 2>/dev/null) &
    fi
    
    # Start server with specified port
    if [ "$PORT" != "8000" ]; then
        if command -v python3 &> /dev/null; then
            python3 server.py $PORT
        else
            python server.py $PORT
        fi
    else
        start_server
    fi
}

# Handle Ctrl+C gracefully
trap 'echo; print_colored $PURPLE "👋 تم إيقاف الخادم بواسطة المستخدم"; exit 0' INT

# Run main function
main
