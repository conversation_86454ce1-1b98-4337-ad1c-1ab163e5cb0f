@echo off
chcp 65001 >nul
title نقرة هوست - Nakra Host Local Server

echo.
echo ========================================
echo 🚀 نقرة هوست - خادم محلي
echo    Nakra Host Local Development Server
echo ========================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python من: https://python.org
    echo.
    pause
    exit /b 1
)

:: Check if required files exist
if not exist "index.html" (
    echo ❌ ملف index.html غير موجود
    echo 💡 تأكد من وجودك في مجلد المشروع الصحيح
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo ✅ تم العثور على ملفات المشروع
echo.

:: Start the server
echo 🔄 بدء تشغيل الخادم...
echo 📍 العنوان: http://localhost:8000
echo 💡 اضغط Ctrl+C لإيقاف الخادم
echo.

python server.py

echo.
echo 👋 تم إيقاف الخادم
pause
