#!/usr/bin/env python3
"""
Simple HTTP Server for Nakra Host Website
تشغيل خادم محلي لموقع نقرة هوست

Usage:
    python server.py [port]
    
Default port: 8000
"""

import http.server
import socketserver
import os
import sys
import webbrowser
import threading
import time
from urllib.parse import urlparse

class NakraHostHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler for Nakra Host website"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Add security headers
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-XSS-Protection', '1; mode=block')
        self.send_header('Referrer-Policy', 'strict-origin-when-cross-origin')
        
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # Cache control
        if self.path.endswith(('.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico')):
            self.send_header('Cache-Control', 'public, max-age=86400')  # 1 day
        else:
            self.send_header('Cache-Control', 'no-cache')
        
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests with custom routing"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Route handling
        if path == '/':
            self.path = '/index.html'
        elif path == '/login':
            self.path = '/login.html'
        elif path == '/register':
            self.path = '/register.html'
        elif not os.path.exists(self.path.lstrip('/')):
            # If file doesn't exist, serve index.html for SPA routing
            if not path.startswith('/api/') and not '.' in os.path.basename(path):
                self.path = '/index.html'
        
        return super().do_GET()
    
    def do_POST(self):
        """Handle POST requests (for form submissions)"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        # Log the request
        print(f"POST {self.path}: {post_data.decode('utf-8', errors='ignore')}")
        
        # Send a simple response
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = {
            'status': 'success',
            'message': 'تم استلام البيانات بنجاح',
            'redirect': 'https://hostmeed.cloud'
        }
        
        import json
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def log_message(self, format, *args):
        """Custom log format"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def check_files():
    """Check if required files exist"""
    required_files = [
        'index.html',
        'css/style.css',
        'js/main.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("⚠️  تحذير: الملفات التالية مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        print()
    
    return len(missing_files) == 0

def open_browser(port, delay=2):
    """Open browser after a delay"""
    time.sleep(delay)
    url = f"http://localhost:{port}"
    print(f"🌐 فتح المتصفح: {url}")
    webbrowser.open(url)

def print_banner(port):
    """Print startup banner"""
    print("=" * 60)
    print("🚀 خادم نقرة هوست المحلي")
    print("   Nakra Host Local Development Server")
    print("=" * 60)
    print(f"📍 العنوان: http://localhost:{port}")
    print(f"📁 المجلد: {os.getcwd()}")
    print("=" * 60)
    print("📋 الصفحات المتاحة:")
    print(f"   🏠 الرئيسية: http://localhost:{port}/")
    print(f"   🔐 تسجيل الدخول: http://localhost:{port}/login")
    print(f"   📝 إنشاء حساب: http://localhost:{port}/register")
    print("=" * 60)
    print("💡 نصائح:")
    print("   - اضغط Ctrl+C لإيقاف الخادم")
    print("   - استخدم Ctrl+F5 لإعادة تحميل الصفحة مع تجاهل الكاش")
    print("   - تحقق من وحدة تحكم المطور (F12) للأخطاء")
    print("=" * 60)

def main():
    """Main function"""
    # Get port from command line or use default
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ رقم المنفذ غير صحيح. استخدام المنفذ الافتراضي 8000")
            port = 8000
    
    # Check if files exist
    if not check_files():
        print("⚠️  بعض الملفات مفقودة، لكن سيتم تشغيل الخادم على أي حال...")
        print()
    
    # Print banner
    print_banner(port)
    
    try:
        # Create server
        with socketserver.TCPServer(("", port), NakraHostHTTPRequestHandler) as httpd:
            print(f"✅ تم تشغيل الخادم على المنفذ {port}")
            print("🔄 في انتظار الطلبات...")
            print()
            
            # Open browser in a separate thread
            browser_thread = threading.Thread(target=open_browser, args=(port,))
            browser_thread.daemon = True
            browser_thread.start()
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n")
        print("🛑 تم إيقاف الخادم بواسطة المستخدم")
        print("👋 شكراً لاستخدام خادم نقرة هوست!")
        
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ المنفذ {port} مستخدم بالفعل")
            print(f"💡 جرب منفذ آخر: python server.py {port + 1}")
        else:
            print(f"❌ خطأ في تشغيل الخادم: {e}")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
