/* 
===========================================
نقرة هوست - ملف CSS الرئيسي الموحد
Nakra Host - Main Unified CSS File
===========================================
*/

/* ===== 1. RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #ffffff;
    direction: rtl;
    text-align: right;
}

/* ===== 2. TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* ===== 3. LAYOUT CONTAINERS ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.section {
    padding: 4rem 0;
}

/* ===== 4. NAVBAR ===== */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 80px;
    display: flex;
    align-items: center;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.navbar .logo {
    height: 50px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
}

.navbar .logo img {
    height: 100%;
    width: auto;
    max-width: 100%;
    object-fit: contain;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin-right: 120px;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #2563eb;
}

/* ===== 5. LANGUAGE SWITCHER ===== */
.language-switcher {
    position: absolute;
    top: 50%;
    right: 1.5rem;
    transform: translateY(-50%);
    z-index: 1001;
}

.lang-btn {
    background: #ffffff !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 0.75rem !important;
    padding: 0.75rem 1rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    text-decoration: none !important;
    min-width: 100px !important;
    justify-content: center !important;
}

.lang-btn:hover {
    background: #f8fafc !important;
    border-color: #2563eb !important;
    color: #2563eb !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 15px rgba(37, 99, 235, 0.15) !important;
}

/* ===== 6. BUTTONS ===== */
.btn {
    display: inline-block;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 0.95rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.25);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
    color: #ffffff;
}

.btn-outline {
    background: transparent;
    color: #2563eb;
    border: 2px solid #2563eb;
}

.btn-outline:hover {
    background: #2563eb;
    color: #ffffff;
}

/* ===== 7. HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: calc(80px + 4rem) 0 4rem;
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.hero-text {
    color: #ffffff;
    padding-right: 2rem;
}

.hero-text h1 {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-text p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-visual {
    position: relative;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 2rem;
}

/* ===== 8. HERO CARDS ===== */
.hero-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    max-width: 500px;
    width: 100%;
}

.floating-card {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 1rem;
    padding: 1.25rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 11;
    isolation: isolate;
}

.floating-card:hover {
    transform: translateY(-6px);
    background: rgba(255, 255, 255, 1) !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    border-color: rgba(37, 99, 235, 0.3);
}

.floating-card .icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.floating-card h4 {
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    color: #1f2937 !important;
    font-weight: 700;
    line-height: 1.2;
}

.floating-card p {
    font-size: 0.8rem;
    color: #6b7280 !important;
    margin: 0;
    line-height: 1.3;
    text-align: center;
}

/* Card specific colors */
.floating-card:nth-child(1) .icon {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

.floating-card:nth-child(2) .icon {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.floating-card:nth-child(3) .icon {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

.floating-card:nth-child(4) .icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
}

/* ===== 9. SECTIONS ===== */
.services {
    background: #f8fafc !important;
}

.section-title {
    color: #111827 !important;
    font-weight: 900 !important;
    text-align: center;
    margin-bottom: 1rem;
}

.services .section-title {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.section-subtitle {
    color: #2563eb !important;
    font-weight: 600 !important;
    font-size: 1.125rem !important;
    text-align: center;
    margin-bottom: 3rem;
}

.section-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    display: inline-block;
    margin-bottom: 1rem;
}

/* ===== 10. RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .hero-text {
        padding-right: 0;
    }
    
    .hero-visual {
        padding-left: 0;
    }
    
    .hero-text h1 {
        font-size: 2rem;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hero-cards {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        max-width: 400px;
    }
    
    .floating-card {
        padding: 1rem;
        min-height: 100px;
    }
    
    .floating-card .icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .floating-card h4 {
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
    }
    
    .floating-card p {
        font-size: 0.75rem;
        line-height: 1.2;
    }
    
    .language-switcher {
        top: 0.5rem;
        right: 1rem;
    }
    
    .lang-btn {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.8rem !important;
        min-width: 80px !important;
    }
}

/* ===== 11. PRICING SECTION ===== */
.pricing {
    background: #f8fafc !important;
}

.pricing-card {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 1.5rem !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease !important;
    padding: 2rem;
    text-align: center;
}

.pricing-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12) !important;
    border-color: #2563eb !important;
}

.pricing-card.featured {
    border: 2px solid #2563eb !important;
    transform: scale(1.05) !important;
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-8px) !important;
}

.pricing-card .btn,
.pricing-card .btn-primary,
.pricing-card .cta-btn {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    border: none !important;
    border-radius: 0.75rem !important;
    padding: 0.875rem 1.5rem !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.25) !important;
    text-transform: none !important;
    letter-spacing: 0.025em !important;
    width: 100% !important;
    margin-top: 1.5rem !important;
    text-decoration: none !important;
    display: inline-block !important;
    text-align: center !important;
}

.pricing-card .btn:hover,
.pricing-card .btn-primary:hover,
.pricing-card .cta-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4) !important;
    color: #ffffff !important;
    text-decoration: none !important;
}

.pricing-card.featured .btn,
.pricing-card.featured .btn-primary,
.pricing-card.featured .cta-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.25) !important;
}

.pricing-card.featured .btn:hover,
.pricing-card.featured .btn-primary:hover,
.pricing-card.featured .cta-btn:hover {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%) !important;
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4) !important;
}

/* ===== 12. CONTACT SECTION ===== */
.contact {
    background: #f8fafc !important;
}

.contact .container {
    background: transparent !important;
}

.contact-form-wrapper {
    background: #ffffff !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 1.5rem !important;
    padding: 2.5rem !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08) !important;
    position: relative;
    overflow: hidden;
}

.contact-form-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #3b82f6, #10b981, #f59e0b);
    border-radius: 1.5rem 1.5rem 0 0;
}

.contact-form h3 {
    color: #111827 !important;
    font-weight: 700 !important;
    margin-bottom: 1.5rem;
    text-align: center;
    font-size: 1.5rem;
}

.contact-form .btn-primary,
.contact-form button[type="submit"],
.contact-form .submit-btn {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    border: none !important;
    border-radius: 0.75rem !important;
    padding: 0.875rem 2rem !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3) !important;
    text-transform: none !important;
    letter-spacing: 0.025em !important;
    width: 100% !important;
    margin-top: 1rem !important;
}

.contact-form .btn-primary:hover,
.contact-form button[type="submit"]:hover,
.contact-form .submit-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4) !important;
    color: #ffffff !important;
}

/* ===== 13. FOOTER ===== */
.footer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
    color: #ffffff !important;
    padding: 3rem 0 1rem;
}

.footer-section h3,
.footer-section h4 {
    color: #ffffff !important;
    font-weight: 700 !important;
    margin-bottom: 1rem;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 400 !important;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #60a5fa !important;
}

/* ===== 14. SOCIAL MEDIA ICONS ===== */
.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-link {
    width: 45px !important;
    height: 45px !important;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #ffffff !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    font-size: 1.125rem !important;
}

.social-link:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4) !important;
    color: #ffffff !important;
}

.social-link.facebook {
    background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%) !important;
}

.social-link.facebook:hover {
    background: linear-gradient(135deg, #166fe5 0%, #1877f2 100%) !important;
    box-shadow: 0 8px 25px rgba(24, 119, 242, 0.4) !important;
}

.social-link.twitter {
    background: linear-gradient(135deg, #1da1f2 0%, #42a5f5 100%) !important;
}

.social-link.twitter:hover {
    background: linear-gradient(135deg, #1a91da 0%, #1da1f2 100%) !important;
    box-shadow: 0 8px 25px rgba(29, 161, 242, 0.4) !important;
}

.social-link.instagram {
    background: linear-gradient(135deg, #e4405f 0%, #f56040 100%) !important;
}

.social-link.instagram:hover {
    background: linear-gradient(135deg, #d73447 0%, #e4405f 100%) !important;
    box-shadow: 0 8px 25px rgba(228, 64, 95, 0.4) !important;
}

.social-link.linkedin {
    background: linear-gradient(135deg, #0077b5 0%, #00a0dc 100%) !important;
}

.social-link.linkedin:hover {
    background: linear-gradient(135deg, #005885 0%, #0077b5 100%) !important;
    box-shadow: 0 8px 25px rgba(0, 119, 181, 0.4) !important;
}

/* ===== 15. ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* ===== 16. UTILITIES ===== */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.grid { display: grid; }

/* ===== END OF CSS ===== */
